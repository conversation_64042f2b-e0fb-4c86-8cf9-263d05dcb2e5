use tauri::State;
use crate::{AppState, models::*};
use crate::auth::check_permission;
use barcoders::sym::ean13::*;
use barcoders::sym::code128::*;
use barcoders::generators::image::*;
use base64::{Engine as _, engine::general_purpose};

#[derive(Debug, serde::Serialize, serde::Deserialize)]
pub struct BarcodeData {
    pub sku: String,
    pub barcode_type: String,
    pub barcode_string: String,
    pub barcode_image_base64: Option<String>,
}

#[derive(Debug, serde::Serialize, serde::Deserialize)]
pub struct PrinterInfo {
    pub name: String,
    pub is_default: bool,
    pub status: String,
}

/// Generate barcode data for a product SKU
#[tauri::command]
pub async fn generate_product_barcode_data(
    token: String,
    product_sku: String,
    _state: State<'_, AppState>,
) -> Result<BarcodeData, String> {
    check_permission(&token, "inventory_management").await?;
    
    println!("Generating barcode for SKU: {}", product_sku);
    
    // Determine barcode type based on SKU format
    let (barcode_type, barcode_string, barcode_image_base64) = if is_ean13_compatible(&product_sku) {
        generate_ean13_barcode(&product_sku)?
    } else {
        generate_code128_barcode(&product_sku)?
    };
    
    Ok(BarcodeData {
        sku: product_sku,
        barcode_type,
        barcode_string,
        barcode_image_base64: Some(barcode_image_base64),
    })
}

/// Check if SKU is compatible with EAN-13 format (12-13 digits)
fn is_ean13_compatible(sku: &str) -> bool {
    sku.chars().all(|c| c.is_ascii_digit()) && (sku.len() == 12 || sku.len() == 13)
}

/// Generate EAN-13 barcode
fn generate_ean13_barcode(sku: &str) -> Result<(String, String, String), String> {
    // Ensure we have exactly 12 digits for EAN-13 (13th digit is check digit)
    let ean_code = if sku.len() == 12 {
        // Let the library calculate the check digit
        sku.to_string()
    } else if sku.len() == 13 {
        // Use the provided EAN-13
        sku.to_string()
    } else {
        return Err("SKU must be 12 or 13 digits for EAN-13".to_string());
    };

    // Create and encode the barcode
    let ean13 = EAN13::new(&ean_code)
        .map_err(|e| format!("Failed to create EAN-13 barcode: {}", e))?;

    let encoded_data = ean13.encode();
    let image_base64 = generate_barcode_image(&encoded_data)?;

    Ok(("EAN-13".to_string(), ean_code, image_base64))
}

/// Generate Code 128 barcode
fn generate_code128_barcode(sku: &str) -> Result<(String, String, String), String> {
    let code128 = Code128::new(sku.to_string())
        .map_err(|e| format!("Failed to create Code 128 barcode: {}", e))?;

    let encoded_data = code128.encode();
    let image_base64 = generate_barcode_image(&encoded_data)?;

    Ok(("Code 128".to_string(), sku.to_string(), image_base64))
}

/// Generate barcode image and return as base64 string
fn generate_barcode_image(encoded_data: &[u8]) -> Result<String, String> {
    // Generate barcode as PNG image
    let png = Image::png(100); // height in pixels
    let bytes = png.generate(encoded_data)
        .map_err(|e| format!("Failed to generate barcode image: {}", e))?;

    // Encode as base64
    let base64_string = general_purpose::STANDARD.encode(&bytes);
    Ok(base64_string)
}

/// Get product by SKU (enhanced for barcode scanning)
#[tauri::command]
pub async fn get_product_by_sku(
    token: String,
    sku: String,
    state: State<'_, AppState>,
) -> Result<Option<Product>, String> {
    check_permission(&token, "sales_management").await?;
    
    let db = state.db.lock().await;
    db.get_product_by_sku(&sku).await
        .map_err(|e| format!("Failed to get product by SKU: {}", e))
}

/// List available printers on the system
#[tauri::command]
pub async fn list_available_printers(
    token: String,
) -> Result<Vec<PrinterInfo>, String> {
    check_permission(&token, "inventory_management").await?;

    println!("Discovering available printers...");

    let printers = printers::get_printers();
    let printer_list: Vec<PrinterInfo> = printers
        .into_iter()
        .map(|printer| PrinterInfo {
            name: printer.name,
            is_default: printer.is_default,
            status: "Available".to_string(), // Basic status for now
        })
        .collect();

    println!("Found {} printers", printer_list.len());
    Ok(printer_list)
}

/// Test print to verify printer connectivity
#[tauri::command]
pub async fn print_test_label(
    token: String,
    printer_name: String,
) -> Result<String, String> {
    check_permission(&token, "inventory_management").await?;
    
    println!("Printing test label to: {}", printer_name);
    
    // Create test label content
    let test_content = format!(
        "ISMS Test Label\nPrinter: {}\nTime: {}\n",
        printer_name,
        chrono::Utc::now().format("%Y-%m-%d %H:%M:%S")
    );
    
    // For now, we'll use basic text printing
    // In a real implementation, this would send raw commands to the thermal printer
    match printers::get_printer_by_name(&printer_name) {
        Some(printer) => {
            match printer.print(test_content.as_bytes(), Some("Test Label")) {
                Ok(_) => {
                    println!("Test label sent to printer successfully");
                    Ok("Test label printed successfully".to_string())
                }
                Err(e) => {
                    println!("Failed to print test label: {}", e);
                    Err(format!("Failed to print test label: {}", e))
                }
            }
        }
        None => {
            println!("Printer '{}' not found", printer_name);
            Err(format!("Printer '{}' not found", printer_name))
        }
    }
}

/// Print barcode label for a product
#[tauri::command]
pub async fn print_barcode_label(
    token: String,
    product_sku: String,
    printer_name: String,
    state: State<'_, AppState>,
) -> Result<String, String> {
    check_permission(&token, "inventory_management").await?;

    println!("Printing barcode label for SKU: {} on printer: {}", product_sku, printer_name);

    // Get product details
    let db = state.db.lock().await;
    let product = db.get_product_by_sku(&product_sku).await
        .map_err(|e| format!("Failed to get product: {}", e))?;

    let product = product.ok_or_else(|| format!("Product with SKU '{}' not found", product_sku))?;

    // Generate barcode data (for future use in thermal printer commands)
    let _barcode_data = if is_ean13_compatible(&product_sku) {
        generate_ean13_barcode(&product_sku)?
    } else {
        generate_code128_barcode(&product_sku)?
    };

    // Create label content for thermal printer
    // This is a basic text format - in production, you'd use ESC/POS or TSPL commands
    let label_content = format!(
        "\n{}\n{}\n{}\nSKU: {}\nPrice: ${:.2}\n\n",
        "=".repeat(32),
        product.name,
        "=".repeat(32),
        product.sku,
        product.price
    );

    // Print to the specified printer
    match printers::get_printer_by_name(&printer_name) {
        Some(printer) => {
            match printer.print(label_content.as_bytes(), Some(&format!("Barcode Label - {}", product.name))) {
                Ok(_) => {
                    println!("Barcode label printed successfully");
                    Ok("Barcode label printed successfully".to_string())
                }
                Err(e) => {
                    println!("Failed to print barcode label: {}", e);
                    Err(format!("Failed to print barcode label: {}", e))
                }
            }
        }
        None => {
            println!("Printer '{}' not found", printer_name);
            Err(format!("Printer '{}' not found", printer_name))
        }
    }
}
