import { CartItem } from '../types';

export interface ReceiptData {
  orderId?: number;
  items: CartItem[];
  subtotal: number;
  tax?: number;
  total: number;
  customerName?: string;
  paymentMethod: string;
  cashier?: string;
  timestamp: Date;
}

/**
 * Generate a formatted receipt string for printing
 */
export const generateReceiptContent = (data: ReceiptData): string => {
  const {
    orderId,
    items,
    subtotal,
    tax = 0,
    total,
    customerName,
    paymentMethod,
    cashier,
    timestamp,
  } = data;

  const lines: string[] = [];
  const width = 48; // Standard receipt width in characters

  // Helper function to center text
  const centerText = (text: string): string => {
    const padding = Math.max(0, Math.floor((width - text.length) / 2));
    return ' '.repeat(padding) + text;
  };

  // Helper function to create a line with left and right aligned text
  const createLine = (left: string, right: string): string => {
    const totalLength = left.length + right.length;
    const spaces = Math.max(1, width - totalLength);
    return left + ' '.repeat(spaces) + right;
  };

  // Header
  lines.push('='.repeat(width));
  lines.push(centerText('ISMS RECEIPT'));
  lines.push(centerText('Inventory Management System'));
  lines.push('='.repeat(width));
  lines.push('');

  // Order info
  if (orderId) {
    lines.push(`Order #: ${orderId}`);
  }
  lines.push(`Date: ${timestamp.toLocaleDateString()}`);
  lines.push(`Time: ${timestamp.toLocaleTimeString()}`);
  if (customerName) {
    lines.push(`Customer: ${customerName}`);
  }
  if (cashier) {
    lines.push(`Cashier: ${cashier}`);
  }
  lines.push('');
  lines.push('-'.repeat(width));

  // Items header
  lines.push('ITEMS');
  lines.push('-'.repeat(width));

  // Items
  items.forEach((item) => {
    const itemTotal = item.product.price * item.quantity;

    // Product name
    lines.push(item.product.name);

    // Quantity, price, and total
    const qtyPrice = `${item.quantity} x $${item.product.price.toFixed(2)}`;
    const itemTotalStr = `$${itemTotal.toFixed(2)}`;
    lines.push(createLine(qtyPrice, itemTotalStr));

    // SKU
    lines.push(`  SKU: ${item.product.sku}`);
    lines.push('');
  });

  lines.push('-'.repeat(width));

  // Totals
  lines.push(createLine('Subtotal:', `$${subtotal.toFixed(2)}`));
  
  if (tax > 0) {
    lines.push(createLine('Tax:', `$${tax.toFixed(2)}`));
  }
  
  lines.push('='.repeat(width));
  lines.push(createLine('TOTAL:', `$${total.toFixed(2)}`));
  lines.push('='.repeat(width));
  lines.push('');

  // Payment info
  lines.push(`Payment Method: ${paymentMethod}`);
  lines.push('');

  // Footer
  lines.push(centerText('Thank you for your business!'));
  lines.push(centerText('Please keep this receipt'));
  lines.push(centerText('for your records'));
  lines.push('');
  lines.push('='.repeat(width));

  return lines.join('\n');
};

/**
 * Generate a simple test receipt
 */
export const generateTestReceipt = (): string => {
  const testData: ReceiptData = {
    orderId: 12345,
    items: [
      {
        product: {
          id: 1,
          name: 'Test Product 1',
          sku: 'TEST001',
          price: 9.99,
          description: 'Test product description',
          category: 'Test',
          cost: 5.00,
          quantity: 100,
          reorder_level: 10,
          expiry_date: undefined,
          supplier_id: undefined,
          last_synced_at: undefined,
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString(),
        },
        quantity: 2,
        subtotal: 19.98,
      },
      {
        product: {
          id: 2,
          name: 'Test Product 2',
          sku: 'TEST002',
          price: 15.50,
          description: 'Test product description 2',
          category: 'Test',
          cost: 8.00,
          quantity: 50,
          reorder_level: 5,
          expiry_date: undefined,
          supplier_id: undefined,
          last_synced_at: undefined,
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString(),
        },
        quantity: 1,
        subtotal: 15.50,
      },
    ],
    subtotal: 35.48,
    tax: 3.55,
    total: 39.03,
    customerName: 'Test Customer',
    paymentMethod: 'Cash',
    cashier: 'Test Cashier',
    timestamp: new Date(),
  };

  return generateReceiptContent(testData);
};

/**
 * Generate a barcode label content for thermal printing
 */
export const generateBarcodeLabel = (
  productName: string,
  sku: string,
  price: number,
  barcodeData?: string
): string => {
  const lines: string[] = [];
  const width = 32; // Label width in characters

  // Helper function to center text
  const centerText = (text: string): string => {
    const padding = Math.max(0, Math.floor((width - text.length) / 2));
    return ' '.repeat(padding) + text;
  };

  lines.push('='.repeat(width));
  lines.push(centerText(productName.substring(0, width - 2)));
  lines.push('='.repeat(width));
  lines.push('');
  
  // SKU
  lines.push(centerText(`SKU: ${sku}`));
  lines.push('');
  
  // Price
  lines.push(centerText(`$${price.toFixed(2)}`));
  lines.push('');
  
  // Barcode representation (if available)
  if (barcodeData) {
    lines.push(centerText('||||| |||| ||||| ||||'));
    lines.push(centerText(barcodeData));
  }
  
  lines.push('');
  lines.push('='.repeat(width));

  return lines.join('\n');
};
