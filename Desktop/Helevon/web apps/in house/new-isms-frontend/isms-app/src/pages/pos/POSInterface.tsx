import React, { useState, useEffect, useRef } from 'react';
import { usePosStore } from '../../store/posStore';
import { posService } from '../../services/posService';
import { useToast } from '../../hooks/useToast';
import { usePOSBarcodeScanner } from '../../hooks/useBarcodeScanner';
import Button from '../../components/ui/Button';
import Card from '../../components/ui/Card';
import { Product } from '../../types';

const POSInterface: React.FC = () => {
  const { showSuccess, showError } = useToast();
  const {
    cart,
    customerName,
    paymentMethod,
    isProcessing,
    addToCart,
    removeFromCart,
    updateCartItemQuantity,
    clearCart,
    setCustomerName,
    setPaymentMethod,
    setProcessing,
    setLastOrderId,
    getCartTotal,
    getCartItemCount,
  } = usePosStore();

  const [searchQuery, setSearchQuery] = useState('');
  const [searchResults, setSearchResults] = useState<Product[]>([]);
  const [isSearching, setIsSearching] = useState(false);
  const [barcodeInput, setBarcodeInput] = useState('');
  const [scannerEnabled, setScannerEnabled] = useState(true);
  const searchInputRef = useRef<HTMLInputElement>(null);
  const barcodeInputRef = useRef<HTMLInputElement>(null);

  // Handle barcode scanning
  const handleBarcodeScanned = async (sku: string) => {
    console.log('Barcode scanned in POS:', sku);

    try {
      const product = await posService.getProductBySku(sku);
      if (product) {
        addToCart(product);
        showSuccess(`Added ${product.name} to cart`);

        // Visual feedback
        setBarcodeInput(sku);
        setTimeout(() => setBarcodeInput(''), 2000);
      } else {
        showError(`Product not found for SKU: ${sku}`);
      }
    } catch (error) {
      console.error('Failed to find product:', error);
      showError('Failed to find product');
    }
  };

  // Initialize barcode scanner
  usePOSBarcodeScanner(handleBarcodeScanned, scannerEnabled);

  // Focus on search input when component mounts
  useEffect(() => {
    if (searchInputRef.current) {
      searchInputRef.current.focus();
    }
  }, []);

  // Handle product search
  const handleSearch = async (query: string) => {
    if (!query.trim()) {
      setSearchResults([]);
      return;
    }

    try {
      setIsSearching(true);
      const results = await posService.searchProductsByName(query.trim());
      setSearchResults(results);
    } catch (error) {
      console.error('Search failed:', error);
      showError('Failed to search products');
    } finally {
      setIsSearching(false);
    }
  };

  // Handle barcode scan
  const handleBarcodeSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!barcodeInput.trim()) return;

    try {
      const product = await posService.processBarcodeSccan(barcodeInput.trim());
      if (product) {
        addToCart(product);
        setBarcodeInput('');
        showSuccess(`Added ${product.name} to cart`);
      } else {
        showError('Product not found');
      }
    } catch (error) {
      console.error('Barcode scan failed:', error);
      showError('Failed to process barcode');
    }
  };

  // Handle checkout
  const handleCheckout = async () => {
    if (cart.length === 0) return;

    try {
      setProcessing(true);

      const orderData = {
        customer_name: customerName || undefined,
        payment_method: paymentMethod,
        items: cart.map(item => ({
          product_id: item.product.id,
          quantity: item.quantity,
          price_at_sale: item.product.price,
        })),
      };

      const orderId = await posService.createOrder(orderData);
      await posService.completeOrder(orderId);

      setLastOrderId(orderId);
      showSuccess(`Order #${orderId} completed successfully`);

      // Print receipt and open cash drawer for cash payments
      if (paymentMethod === 'cash') {
        try {
          await posService.openCashDrawer();
          await posService.printReceipt(orderId);
        } catch (error) {
          console.error('Hardware operation failed:', error);
          // Don't show error for hardware operations in demo
        }
      }

      clearCart();
      setSearchQuery('');
      setSearchResults([]);

      // Focus back on search input
      if (searchInputRef.current) {
        searchInputRef.current.focus();
      }
    } catch (error) {
      console.error('Checkout failed:', error);
      showError('Failed to complete order');
    } finally {
      setProcessing(false);
    }
  };

  const cartTotal = getCartTotal();
  const cartItemCount = getCartItemCount();

  return (
    <div className="h-full flex flex-col lg:flex-row gap-6">
      {/* Left Panel - Product Search and Selection */}
      <div className="flex-1 space-y-6">
        <Card title="Product Search" className="h-full">
          <div className="space-y-4">
            {/* Scanner Status */}
            <div className="flex items-center justify-between p-3 bg-gray-50 dark:bg-gray-800 rounded-lg">
              <div className="flex items-center space-x-2">
                <div className={`h-2 w-2 rounded-full ${scannerEnabled ? 'bg-green-500' : 'bg-red-500'}`}></div>
                <span className="text-sm text-gray-700 dark:text-gray-300">
                  Barcode Scanner: {scannerEnabled ? 'Active' : 'Disabled'}
                </span>
              </div>
              <button
                type="button"
                onClick={() => setScannerEnabled(!scannerEnabled)}
                className="text-xs text-primary-600 hover:text-primary-700 dark:text-primary-400"
              >
                {scannerEnabled ? 'Disable' : 'Enable'}
              </button>
            </div>

            {/* Barcode Scanner */}
            <form onSubmit={handleBarcodeSubmit} className="flex gap-2">
              <input
                ref={barcodeInputRef}
                type="text"
                placeholder="Scan barcode or enter SKU..."
                value={barcodeInput}
                onChange={(e) => setBarcodeInput(e.target.value)}
                className="flex-1 px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent bg-white dark:bg-gray-800 text-gray-900 dark:text-white"
              />
              <Button type="submit" disabled={!barcodeInput.trim()}>
                Add
              </Button>
            </form>

            {/* Product Search */}
            <div>
              <input
                ref={searchInputRef}
                type="text"
                placeholder="Search products by name..."
                value={searchQuery}
                onChange={(e) => {
                  setSearchQuery(e.target.value);
                  handleSearch(e.target.value);
                }}
                className="w-full px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent bg-white dark:bg-gray-800 text-gray-900 dark:text-white"
              />
            </div>

            {/* Search Results */}
            <div className="max-h-96 overflow-y-auto">
              {isSearching ? (
                <div className="text-center py-8">
                  <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary-600 mx-auto"></div>
                  <p className="mt-2 text-gray-600 dark:text-gray-400">Searching...</p>
                </div>
              ) : searchResults.length > 0 ? (
                <div className="space-y-2">
                  {searchResults.map((product) => (
                    <div
                      key={product.id}
                      className="flex items-center justify-between p-3 bg-gray-50 dark:bg-gray-800 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-700 cursor-pointer"
                      onClick={() => {
                        addToCart(product);
                        showSuccess(`Added ${product.name} to cart`);
                      }}
                    >
                      <div className="flex-1">
                        <h4 className="font-medium text-gray-900 dark:text-white">{product.name}</h4>
                        <p className="text-sm text-gray-600 dark:text-gray-400">
                          SKU: {product.sku} | Stock: {product.quantity}
                        </p>
                      </div>
                      <div className="text-right">
                        <p className="font-semibold text-gray-900 dark:text-white">
                          ${product.price.toFixed(2)}
                        </p>
                        {product.quantity <= product.reorder_level && (
                          <p className="text-xs text-red-600 dark:text-red-400">Low Stock</p>
                        )}
                      </div>
                    </div>
                  ))}
                </div>
              ) : searchQuery && !isSearching ? (
                <div className="text-center py-8">
                  <p className="text-gray-600 dark:text-gray-400">No products found</p>
                </div>
              ) : null}
            </div>
          </div>
        </Card>
      </div>

      {/* Right Panel - Cart and Checkout */}
      <div className="w-full lg:w-96 space-y-6">
        {/* Cart */}
        <Card title={`Cart (${cartItemCount} items)`}>
          <div className="space-y-4">
            {cart.length > 0 ? (
              <>
                <div className="max-h-64 overflow-y-auto space-y-2">
                  {cart.map((item) => (
                    <div key={item.product.id} className="flex items-center justify-between p-2 bg-gray-50 dark:bg-gray-800 rounded">
                      <div className="flex-1">
                        <h5 className="font-medium text-sm text-gray-900 dark:text-white">
                          {item.product.name}
                        </h5>
                        <p className="text-xs text-gray-600 dark:text-gray-400">
                          ${item.product.price.toFixed(2)} each
                        </p>
                      </div>
                      <div className="flex items-center space-x-2">
                        <button
                          onClick={() => updateCartItemQuantity(item.product.id, item.quantity - 1)}
                          className="w-6 h-6 rounded-full bg-gray-200 dark:bg-gray-700 flex items-center justify-center text-gray-600 dark:text-gray-400 hover:bg-gray-300 dark:hover:bg-gray-600"
                        >
                          -
                        </button>
                        <span className="w-8 text-center text-sm font-medium text-gray-900 dark:text-white">
                          {item.quantity}
                        </span>
                        <button
                          onClick={() => updateCartItemQuantity(item.product.id, item.quantity + 1)}
                          className="w-6 h-6 rounded-full bg-gray-200 dark:bg-gray-700 flex items-center justify-center text-gray-600 dark:text-gray-400 hover:bg-gray-300 dark:hover:bg-gray-600"
                        >
                          +
                        </button>
                        <button
                          onClick={() => removeFromCart(item.product.id)}
                          className="w-6 h-6 rounded-full bg-red-100 dark:bg-red-900/20 flex items-center justify-center text-red-600 dark:text-red-400 hover:bg-red-200 dark:hover:bg-red-900/40"
                        >
                          ×
                        </button>
                      </div>
                    </div>
                  ))}
                </div>
                
                <div className="border-t border-gray-200 dark:border-gray-700 pt-4">
                  <div className="flex justify-between items-center text-lg font-semibold text-gray-900 dark:text-white">
                    <span>Total:</span>
                    <span>${cartTotal.toFixed(2)}</span>
                  </div>
                </div>
              </>
            ) : (
              <div className="text-center py-8">
                <svg className="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 3h2l.4 2M7 13h10l4-8H5.4m0 0L7 13m0 0l-2.5 5M7 13l2.5 5m6-5v6a2 2 0 11-4 0v-6m4 0V9a2 2 0 10-4 0v4.01" />
                </svg>
                <p className="mt-2 text-gray-600 dark:text-gray-400">Cart is empty</p>
              </div>
            )}
          </div>
        </Card>

        {/* Checkout */}
        {cart.length > 0 && (
          <Card title="Checkout">
            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                  Customer Name (Optional)
                </label>
                <input
                  type="text"
                  value={customerName}
                  onChange={(e) => setCustomerName(e.target.value)}
                  placeholder="Enter customer name..."
                  className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent bg-white dark:bg-gray-800 text-gray-900 dark:text-white"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                  Payment Method
                </label>
                <select
                  value={paymentMethod}
                  onChange={(e) => setPaymentMethod(e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent bg-white dark:bg-gray-800 text-gray-900 dark:text-white"
                >
                  <option value="cash">Cash</option>
                  <option value="card">Card</option>
                  <option value="mobile">Mobile Payment</option>
                </select>
              </div>

              <div className="space-y-2">
                <Button
                  onClick={handleCheckout}
                  disabled={isProcessing}
                  loading={isProcessing}
                  className="w-full"
                  size="lg"
                >
                  {isProcessing ? 'Processing...' : `Complete Sale - $${cartTotal.toFixed(2)}`}
                </Button>
                
                <Button
                  variant="outline"
                  onClick={clearCart}
                  disabled={isProcessing}
                  className="w-full"
                >
                  Clear Cart
                </Button>
              </div>
            </div>
          </Card>
        )}
      </div>
    </div>
  );
};

export default POSInterface;
