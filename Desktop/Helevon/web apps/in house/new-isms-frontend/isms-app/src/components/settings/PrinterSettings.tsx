import React, { useState, useEffect } from 'react';
import { useAuthStore } from '../../store/authStore';
import { useToast } from '../../hooks/useToast';
import { tauriApi } from '../../services/tauri';
import Card from '../ui/Card';
import Button from '../ui/Button';

interface PrinterInfo {
  name: string;
  is_default: boolean;
  status: string;
}

const PrinterSettings: React.FC = () => {
  const { authToken } = useAuthStore();
  const { showSuccess, showError } = useToast();
  
  const [printers, setPrinters] = useState<PrinterInfo[]>([]);
  const [selectedLabelPrinter, setSelectedLabelPrinter] = useState<string>('');
  const [loading, setLoading] = useState(false);
  const [testPrinting, setTestPrinting] = useState<string | null>(null);

  // Load available printers on component mount
  useEffect(() => {
    loadPrinters();
    loadSavedSettings();
  }, []);

  const loadPrinters = async () => {
    if (!authToken) return;
    
    try {
      setLoading(true);
      const printerList = await tauriApi.listAvailablePrinters(authToken);
      setPrinters(printerList);
    } catch (error) {
      console.error('Failed to load printers:', error);
      showError('Failed to load available printers');
    } finally {
      setLoading(false);
    }
  };

  const loadSavedSettings = () => {
    // Load saved printer settings from localStorage
    const savedLabelPrinter = localStorage.getItem('selectedLabelPrinter');
    if (savedLabelPrinter) {
      setSelectedLabelPrinter(savedLabelPrinter);
    }
  };

  const handleLabelPrinterChange = (printerName: string) => {
    setSelectedLabelPrinter(printerName);
    // Save to localStorage
    localStorage.setItem('selectedLabelPrinter', printerName);
    showSuccess('Label printer updated successfully');
  };

  const handleTestPrint = async (printerName: string) => {
    if (!authToken) return;
    
    try {
      setTestPrinting(printerName);
      await tauriApi.printTestLabel(authToken, printerName);
      showSuccess(`Test label sent to ${printerName}`);
    } catch (error) {
      console.error('Failed to print test label:', error);
      showError(`Failed to print test label: ${tauriApi.handleTauriError(error)}`);
    } finally {
      setTestPrinting(null);
    }
  };

  const refreshPrinters = () => {
    loadPrinters();
    showSuccess('Printer list refreshed');
  };

  return (
    <Card title="Printer Settings" description="Configure barcode label and receipt printers">
      <div className="space-y-6">
        {/* Printer Discovery */}
        <div>
          <div className="flex items-center justify-between mb-4">
            <h3 className="text-lg font-medium text-foreground">Available Printers</h3>
            <Button 
              variant="outline" 
              size="sm" 
              onClick={refreshPrinters}
              disabled={loading}
            >
              {loading ? 'Refreshing...' : 'Refresh'}
            </Button>
          </div>
          
          {printers.length === 0 ? (
            <div className="text-center py-8 text-muted-foreground">
              {loading ? 'Loading printers...' : 'No printers found. Make sure your printers are connected and try refreshing.'}
            </div>
          ) : (
            <div className="space-y-3">
              {printers.map((printer) => (
                <div 
                  key={printer.name} 
                  className="flex items-center justify-between p-4 border border-border rounded-lg bg-card"
                >
                  <div className="flex items-center space-x-3">
                    <div className={`h-3 w-3 rounded-full ${
                      printer.status === 'Available' ? 'bg-green-500' : 'bg-red-500'
                    }`}></div>
                    <div>
                      <div className="font-medium text-foreground">{printer.name}</div>
                      <div className="text-sm text-muted-foreground">
                        {printer.is_default ? 'Default Printer' : 'Available'} • {printer.status}
                      </div>
                    </div>
                  </div>
                  
                  <div className="flex items-center space-x-2">
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => handleTestPrint(printer.name)}
                      disabled={testPrinting === printer.name}
                    >
                      {testPrinting === printer.name ? 'Printing...' : 'Test Print'}
                    </Button>
                  </div>
                </div>
              ))}
            </div>
          )}
        </div>

        {/* Label Printer Selection */}
        <div>
          <h3 className="text-lg font-medium text-foreground mb-4">Label Printer Configuration</h3>
          <div className="space-y-4">
            <div>
              <label className="block text-sm font-medium text-foreground mb-2">
                Select Label Printer (for barcode labels)
              </label>
              <select
                value={selectedLabelPrinter}
                onChange={(e) => handleLabelPrinterChange(e.target.value)}
                className="w-full px-3 py-2 border border-border rounded-lg bg-background text-foreground focus:ring-2 focus:ring-primary focus:border-transparent"
              >
                <option value="">Select a printer...</option>
                {printers.map((printer) => (
                  <option key={printer.name} value={printer.name}>
                    {printer.name} {printer.is_default ? '(Default)' : ''}
                  </option>
                ))}
              </select>
              <p className="mt-2 text-sm text-muted-foreground">
                This printer will be used for printing barcode labels. Recommended: KATASYMBOL T50M Pro Label Printer.
              </p>
            </div>

            {selectedLabelPrinter && (
              <div className="p-4 bg-muted rounded-lg">
                <div className="flex items-center space-x-2 mb-2">
                  <div className="h-2 w-2 bg-green-500 rounded-full"></div>
                  <span className="text-sm font-medium text-foreground">
                    Label Printer: {selectedLabelPrinter}
                  </span>
                </div>
                <p className="text-sm text-muted-foreground">
                  This printer will be used for all barcode label printing operations.
                </p>
              </div>
            )}
          </div>
        </div>

        {/* Receipt Printer Info */}
        <div>
          <h3 className="text-lg font-medium text-foreground mb-4">Receipt Printer</h3>
          <div className="p-4 bg-muted rounded-lg">
            <p className="text-sm text-muted-foreground">
              Receipt printing uses the standard operating system print dialog. 
              You can select any available printer when printing receipts from the POS system.
            </p>
          </div>
        </div>

        {/* Hardware Integration Info */}
        <div>
          <h3 className="text-lg font-medium text-foreground mb-4">Hardware Integration</h3>
          <div className="space-y-3">
            <div className="p-4 border border-border rounded-lg">
              <div className="flex items-center space-x-2 mb-2">
                <div className="h-2 w-2 bg-blue-500 rounded-full"></div>
                <span className="text-sm font-medium text-foreground">Barcode Scanner</span>
              </div>
              <p className="text-sm text-muted-foreground">
                Inateck BCST-21-AI scanner is supported via HID keyboard emulation. 
                No additional configuration required.
              </p>
            </div>
            
            <div className="p-4 border border-border rounded-lg">
              <div className="flex items-center space-x-2 mb-2">
                <div className="h-2 w-2 bg-purple-500 rounded-full"></div>
                <span className="text-sm font-medium text-foreground">Label Printer</span>
              </div>
              <p className="text-sm text-muted-foreground">
                KATASYMBOL T50M Pro Label Printer appears as "CUPS-BRF-Printer-2" 
                (Generic Text-Only Printer) when connected via Bluetooth.
              </p>
            </div>
          </div>
        </div>
      </div>
    </Card>
  );
};

export default PrinterSettings;
