import { useEffect, useRef, useCallback } from 'react';

interface BarcodeScannerOptions {
  onScan: (barcode: string) => void;
  minLength?: number;
  maxLength?: number;
  timeout?: number;
  enabled?: boolean;
  ignoreInputs?: boolean;
}

/**
 * Custom hook for handling barcode scanner input
 * 
 * The Inateck BCST-21-AI scanner functions as a USB/Bluetooth HID (keyboard emulation),
 * so it sends keystrokes followed by an Enter key press.
 * 
 * This hook accumulates characters until Enter is detected, then processes the barcode.
 */
export const useBarcodeScanner = ({
  onScan,
  minLength = 3,
  maxLength = 50,
  timeout = 100,
  enabled = true,
  ignoreInputs = true,
}: BarcodeScannerOptions) => {
  const bufferRef = useRef<string>('');
  const timeoutRef = useRef<NodeJS.Timeout | null>(null);
  const lastKeypressRef = useRef<number>(0);

  const resetBuffer = useCallback(() => {
    bufferRef.current = '';
    if (timeoutRef.current) {
      clearTimeout(timeoutRef.current);
      timeoutRef.current = null;
    }
  }, []);

  const processBarcode = useCallback((barcode: string) => {
    const trimmedBarcode = barcode.trim();
    
    // Validate barcode length
    if (trimmedBarcode.length >= minLength && trimmedBarcode.length <= maxLength) {
      console.log('Barcode scanned:', trimmedBarcode);
      onScan(trimmedBarcode);
    } else {
      console.log('Invalid barcode length:', trimmedBarcode.length, 'characters');
    }
    
    resetBuffer();
  }, [onScan, minLength, maxLength, resetBuffer]);

  const handleKeyPress = useCallback((event: KeyboardEvent) => {
    if (!enabled) return;

    // Check if we should ignore input from form elements
    if (ignoreInputs) {
      const target = event.target as HTMLElement;
      const tagName = target.tagName.toLowerCase();
      const isInput = ['input', 'textarea', 'select'].includes(tagName);
      const isContentEditable = target.contentEditable === 'true';
      
      if (isInput || isContentEditable) {
        return;
      }
    }

    const currentTime = Date.now();
    const timeDiff = currentTime - lastKeypressRef.current;
    lastKeypressRef.current = currentTime;

    // If too much time has passed since last keypress, reset buffer
    // This helps distinguish between manual typing and scanner input
    if (timeDiff > timeout && bufferRef.current.length > 0) {
      resetBuffer();
    }

    // Handle Enter key (scanner typically sends this after the barcode)
    if (event.key === 'Enter') {
      event.preventDefault();
      if (bufferRef.current.length > 0) {
        processBarcode(bufferRef.current);
      }
      return;
    }

    // Handle Escape key to clear buffer
    if (event.key === 'Escape') {
      resetBuffer();
      return;
    }

    // Accumulate printable characters
    if (event.key.length === 1) {
      bufferRef.current += event.key;
      
      // Set timeout to auto-process if no Enter key is received
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current);
      }
      
      timeoutRef.current = setTimeout(() => {
        if (bufferRef.current.length > 0) {
          processBarcode(bufferRef.current);
        }
      }, timeout * 10); // Longer timeout for auto-processing
    }
  }, [enabled, ignoreInputs, timeout, resetBuffer, processBarcode]);

  useEffect(() => {
    if (!enabled) return;

    // Add event listener for keydown events
    document.addEventListener('keydown', handleKeyPress);

    // Cleanup function
    return () => {
      document.removeEventListener('keydown', handleKeyPress);
      resetBuffer();
    };
  }, [enabled, handleKeyPress, resetBuffer]);

  // Return utility functions
  return {
    resetBuffer,
    getCurrentBuffer: () => bufferRef.current,
  };
};

/**
 * Hook specifically for POS barcode scanning
 */
export const usePOSBarcodeScanner = (onProductScanned: (sku: string) => void, enabled: boolean = true) => {
  return useBarcodeScanner({
    onScan: onProductScanned,
    minLength: 3,
    maxLength: 50,
    timeout: 100,
    enabled,
    ignoreInputs: true,
  });
};

/**
 * Hook for general barcode scanning with custom validation
 */
export const useCustomBarcodeScanner = (
  onScan: (barcode: string) => void,
  options?: Partial<BarcodeScannerOptions>
) => {
  return useBarcodeScanner({
    onScan,
    minLength: 3,
    maxLength: 50,
    timeout: 100,
    enabled: true,
    ignoreInputs: true,
    ...options,
  });
};
