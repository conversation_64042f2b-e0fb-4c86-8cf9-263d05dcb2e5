import { secureInvoke } from '../utils/apiInterceptor';
import { Product, Order, OrderItem, CreateOrderRequest } from '../types';

export const posService = {
  // Product search
  searchProductsBySku: async (sku: string): Promise<Product | null> => {
    return await secureInvoke('search_products_by_sku', { sku });
  },

  searchProductsByName: async (query: string): Promise<Product[]> => {
    return await secureInvoke('search_products_by_name', { query });
  },

  // Order management
  createOrder: async (orderData: CreateOrderRequest): Promise<number> => {
    return await secureInvoke('create_order', { orderData });
  },

  completeOrder: async (orderId: number): Promise<void> => {
    return await secureInvoke('complete_order', { orderId });
  },

  cancelOrder: async (orderId: number): Promise<void> => {
    return await secureInvoke('cancel_order', { orderId });
  },

  getRecentOrders: async (limit?: number): Promise<Order[]> => {
    return await secureInvoke('get_recent_orders', { limit });
  },

  getOrderItems: async (orderId: number): Promise<OrderItem[]> => {
    return await secureInvoke('get_order_items', { orderId });
  },

  // Hardware integration
  processBarcodeSccan: async (barcode: string): Promise<Product | null> => {
    return await secureInvoke('process_barcode_scan', { barcode });
  },

  // Enhanced barcode scanning (using new barcode module)
  getProductBySku: async (sku: string): Promise<Product | null> => {
    return await secureInvoke('get_product_by_sku', { sku });
  },

  printReceipt: async (orderId: number): Promise<string> => {
    return await secureInvoke('print_receipt', { orderId });
  },

  // Enhanced receipt printing with custom content
  printReceiptDialog: async (receiptContent: string, orderId?: number): Promise<string> => {
    return await secureInvoke('print_receipt_dialog', { receiptContent, orderId });
  },

  openCashDrawer: async (): Promise<string> => {
    return await secureInvoke('open_cash_drawer');
  },
};
