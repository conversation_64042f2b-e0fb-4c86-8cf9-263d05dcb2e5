import { invoke } from '@tauri-apps/api/core';
import { LoginResponse, UserInfo } from '../types';

// Tauri command wrappers
export const tauriApi = {
  // Database initialization
  initDatabase: async (): Promise<string> => {
    return await invoke('init_database');
  },

  // Authentication commands
  login: async (email: string, password: string): Promise<LoginResponse> => {
    return await invoke('login', { email, password });
  },

  logout: async (token: string): Promise<string> => {
    return await invoke('logout', { token });
  },

  getCurrentUser: async (token: string): Promise<UserInfo> => {
    return await invoke('get_current_user', { token });
  },

  // Barcode and Printer commands
  generateProductBarcodeData: async (token: string, productSku: string): Promise<any> => {
    return await invoke('generate_product_barcode_data', { token, productSku });
  },

  getProductBySku: async (token: string, sku: string): Promise<any> => {
    return await invoke('get_product_by_sku', { token, sku });
  },

  listAvailablePrinters: async (token: string): Promise<any[]> => {
    return await invoke('list_available_printers', { token });
  },

  printTestLabel: async (token: string, printerName: string): Promise<string> => {
    return await invoke('print_test_label', { token, printerName });
  },

  printBarcodeLabel: async (token: string, productSku: string, printerName: string): Promise<string> => {
    return await invoke('print_barcode_label', { token, productSku, printerName });
  },

  // Utility function to handle Tauri errors
  handleTauriError: (error: any): string => {
    if (typeof error === 'string') {
      return error;
    }
    if (error?.message) {
      return error.message;
    }
    return 'An unexpected error occurred';
  },
};

export default tauriApi;
